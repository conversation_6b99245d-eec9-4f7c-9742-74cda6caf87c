import { logger } from "../../../helpers/logger.js";
import { ServiceContainer } from "../../../service/container/service-container.js";
import type { IPayrixMerchantValidationService, IPayrixPaymentService } from "../../../service/interfaces/payrix-service.interfaces.js";
import type { TokenPaymentRequest } from "../validators/token-payment.validator.js";

export interface PaymentProcessingResult {
  success: boolean;
  transaction?: {
    id: string;
    status: string;
    amount: string;
    description: string;
    created: string;
  };
  merchantInfo?: {
    id: string;
    name: string;
    status: number;
  };
  error?: string;
}

export class TokenPaymentProcessorService {
  private merchantValidationService: IPayrixMerchantValidationService;
  private paymentService: IPayrixPaymentService;

  constructor(merchantValidationService?: IPayrixMerchantValidationService, paymentService?: IPayrixPaymentService) {
    const serviceContainer = ServiceContainer.getInstance();
    this.merchantValidationService = merchantValidationService || serviceContainer.getMerchantValidationService();
    this.paymentService = paymentService || serviceContainer.getPaymentService();
  }

  async processPayment(paymentData: TokenPaymentRequest): Promise<PaymentProcessingResult> {
    try {
      logger.info("Starting token payment processing", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        amount: paymentData.amount,
        description: paymentData.description,
      });

      const merchantValidation = await this.validateMerchant(paymentData.merchantId);
      if (!merchantValidation.success) {
        return merchantValidation;
      }

      const paymentResult = await this.executePayment(paymentData);
      if (!paymentResult.success) {
        return paymentResult;
      }

      await this.cleanupToken(paymentData.token);

      return {
        success: true,
        transaction: {
          id: ((paymentResult.transaction as Record<string, unknown>)?.id as string) || "",
          status: ((paymentResult.transaction as Record<string, unknown>)?.status as string) || "",
          amount: ((paymentResult.transaction as Record<string, unknown>)?.total as string) || paymentData.amount.toString(),
          description: ((paymentResult.transaction as Record<string, unknown>)?.description as string) || paymentData.description || "",
          created: ((paymentResult.transaction as Record<string, unknown>)?.created as string) || new Date().toISOString(),
        },
        merchantInfo: merchantValidation.merchantInfo,
      };
    } catch (error) {
      logger.error("Unexpected error in payment processing", {
        error: error instanceof Error ? error.message : "Unknown error",
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
      });

      await this.cleanupToken(paymentData.token);

      return {
        success: false,
        error: "Internal payment processing error",
      };
    }
  }

  private async validateMerchant(merchantId: string): Promise<PaymentProcessingResult> {
    try {
      logger.info("Validating merchant", { merchantId });

      const validation = await this.merchantValidationService.validateMerchantById(merchantId);

      if (!validation.isValid) {
        logger.warn("Merchant validation failed", {
          merchantId,
          error: validation.error,
        });

        return {
          success: false,
          error: validation.error || "Invalid or inactive merchant",
        };
      }

      logger.info("Merchant validation successful", {
        merchantId,
        merchantName: validation.merchant?.name,
      });

      return {
        success: true,
        merchantInfo: {
          id: (validation.merchant?.id as string) || "",
          name: (validation.merchant?.name as string) || "",
          status: (validation.merchant?.status as number) || 0,
        },
      };
    } catch (error) {
      logger.error("Error during merchant validation", {
        merchantId,
        error: error instanceof Error ? error.message : "Unknown error",
      });

      return {
        success: false,
        error: "Merchant validation failed",
      };
    }
  }

  private async executePayment(paymentData: TokenPaymentRequest): Promise<PaymentProcessingResult> {
    try {
      logger.info("Executing token payment", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        amount: paymentData.amount,
      });

      const paymentResult = await this.paymentService.processTokenPayment({
        merchantId: paymentData.merchantId,
        token: paymentData.token,
        amount: paymentData.amount,
        description: paymentData.description || "Token-based payment",
        customerInfo: paymentData.customerInfo,
      });

      if (!paymentResult.success) {
        logger.error("Token payment processing failed", {
          merchantId: paymentData.merchantId,
          token: paymentData.token.substring(0, 8) + "...",
          error: paymentResult.error,
        });

        return {
          success: false,
          error: paymentResult.error || "Failed to process payment with token",
        };
      }

      logger.info("Token payment processed successfully", {
        merchantId: paymentData.merchantId,
        transactionId: paymentResult.transaction?.id,
        amount: paymentData.amount,
        status: paymentResult.transaction?.status,
      });

      return {
        success: true,
        transaction: {
          id: (paymentResult.transaction?.id as string) || "",
          status: (paymentResult.transaction?.status as string) || "",
          amount: ((paymentResult.transaction as Record<string, unknown>)?.total as string) || paymentData.amount.toString(),
          description: (paymentResult.transaction?.description as string) || paymentData.description || "",
          created: ((paymentResult.transaction as Record<string, unknown>)?.created as string) || new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error("Error during payment execution", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        error: error instanceof Error ? error.message : "Unknown error",
      });

      return {
        success: false,
        error: "Payment execution failed",
      };
    }
  }

  private async cleanupToken(token: string): Promise<void> {
    try {
      await this.paymentService.deleteToken(token);
      logger.info("Token cleanup successful", {
        token: token.substring(0, 8) + "...",
      });
    } catch (cleanupError) {
      logger.warn("Token cleanup failed (payment still successful)", {
        token: token.substring(0, 8) + "...",
        cleanupError: cleanupError instanceof Error ? cleanupError.message : "Unknown cleanup error",
      });
    }
  }
}
