import { logger } from "../../../helpers/logger.js";
import { OnboardingRequest } from "../schemas/onboarding.schema.js";
import { UserAccountOrchestratorService } from "./user-account-orchestrator.service.js";
import { VerificationOrchestratorService } from "./verification-orchestrator.service.js";
import { MerchantCreationOrchestratorService } from "./merchant-creation-orchestrator.service.js";
import { buildSuccessResponse } from "../utils/response-builders.js";
import { handlePayrixError } from "../utils/error-handling.js";

import type { APIGatewayProxyResult } from "aws-lambda";

export interface OnboardingResult {
  success: boolean;
  response?: APIGatewayProxyResult;
  error?: string;
}

export async function orchestrateOnboarding(data: OnboardingRequest, requestId: string): Promise<OnboardingResult> {
  logger.info("Processing direct Payrix submission", {
    requestId,
    email: data.email,
    legalName: data.name,
    clientIp: data.clientIp,
  });

  try {
    const merchantCreationService = new MerchantCreationOrchestratorService();
    const userAccountService = new UserAccountOrchestratorService();
    const verificationService = new VerificationOrchestratorService();

    const merchantResult = await merchantCreationService.createMerchantWithPreparation(data, requestId);

    if (!merchantResult.success || !merchantResult.merchantId || !merchantResult.merchantData) {
      throw new Error(merchantResult.error || "Merchant creation failed");
    }

    const payrixEntityId = merchantResult.merchantId;
    const payrixResponse = merchantResult.merchantData;

    const userAccountResult = await userAccountService.createUserAccountWithErrorHandling(data, payrixEntityId, requestId);

    const verificationResult = await verificationService.processVerification(data, payrixEntityId, requestId, userAccountResult?.data?.id);

    return {
      success: true,
      response: buildSuccessResponse(
        data,
        payrixResponse,
        payrixEntityId,
        userAccountResult.success ? userAccountResult.data || null : null,
        verificationResult
      ),
    };
  } catch (payrixError) {
    return {
      success: false,
      response: handlePayrixError(payrixError as Error, requestId),
    };
  }
}
