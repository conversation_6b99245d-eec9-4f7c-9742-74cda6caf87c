import { logger } from "../../../helpers/logger.js";
import { ServiceContainer } from "../../../service/container/service-container.js";
import { processBankVerification, VerificationFile, VerificationUploadResult } from "./bank-verification.service.js";
import { processPlaidVerification } from "./plaid-verification.service.js";
import type { IPayrixNoteManagementService } from "../../../service/interfaces/payrix-service.interfaces.js";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

export class VerificationOrchestratorService {
  private noteManagementService: IPayrixNoteManagementService;

  constructor(noteManagementService?: IPayrixNoteManagementService) {
    const serviceContainer = ServiceContainer.getInstance();
    this.noteManagementService = noteManagementService || serviceContainer.getNoteManagementService();
  }

  async processVerification(
    data: OnboardingRequest,
    payrixEntityId: string,
    requestId: string,
    userAccountId?: string
  ): Promise<VerificationUploadResult | null> {
    if (!data.bankVerification?.verificationMethod) {
      return null;
    }

    const verificationMethod = data.bankVerification.verificationMethod;

    if (verificationMethod === "manual" && data.bankVerification.verificationFile) {
      return await this.processManualVerification(
        data.bankVerification.verificationFile as VerificationFile,
        payrixEntityId,
        requestId,
        verificationMethod,
        userAccountId
      );
    }

    if (verificationMethod === "plaid") {
      return await processPlaidVerification(this.noteManagementService, payrixEntityId, data.bankVerification.plaidData, requestId, userAccountId);
    }

    logger.warn("Invalid bank verification configuration", {
      requestId,
      payrixEntityId,
      verificationMethod,
      hasFile: !!data.bankVerification.verificationFile,
    });

    return {
      success: false,
      error: "Invalid bank verification configuration",
    };
  }

  private async processManualVerification(
    verificationFile: VerificationFile,
    payrixEntityId: string,
    requestId: string,
    verificationMethod: string,
    userAccountId?: string
  ): Promise<VerificationUploadResult> {
    try {
      if (!userAccountId) {
        logger.warn("User account creation failed or incomplete - proceeding with bank verification without login context", {
          requestId,
          payrixEntityId,
        });
      }

      logger.info("Processing bank verification upload (priority operation)", {
        requestId,
        payrixEntityId,
        verificationMethod,
        fileName: verificationFile.name,
        fileSize: verificationFile.size,
        userAccountId,
        hasLoginContext: !!userAccountId,
      });

      const result = await processBankVerification(
        this.noteManagementService,
        payrixEntityId,
        verificationFile,
        requestId,
        verificationMethod,
        userAccountId
      );

      logger.info("Bank verification upload completed successfully", {
        requestId,
        payrixEntityId,
        success: result.success,
        noteId: result.noteId,
        documentId: result.documentId,
      });

      return result;
    } catch (verificationError) {
      const errorMessage = verificationError instanceof Error ? verificationError.message : String(verificationError);
      logger.error("Bank verification upload failed", {
        requestId,
        payrixEntityId,
        error: verificationError,
        errorMessage,
        errorStack: verificationError instanceof Error ? verificationError.stack : undefined,
      });

      return {
        success: false,
        error: `Bank verification failed: ${errorMessage}`,
      };
    }
  }
}
