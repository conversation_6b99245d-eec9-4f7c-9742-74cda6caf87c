import { AxiosError, AxiosInstance } from "axios";
import { logger } from "../../helpers/logger.js";
import { TokenPaymentData, PaymentResult, TokenDeletionResult, PayrixError } from "../../types/payrix.types.js";
import { createPayrixApiClient, PAYRIX_API_URL } from "./api-client.js";

export class PayrixPaymentService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult> {
    try {
      logger.info("Processing token payment", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        amount: paymentData.amount,
        description: paymentData.description,
      });

      // This endpoint processes actual payments using tokens generated from $0 authorization
      // Always use sale transaction type ("1") for actual payment processing
      // The $0 authorization was already done during token generation in PayFields

      const transactionData = {
        merchant: paymentData.merchantId,
        type: "1", // Always use "1" (sale) for actual payment processing with tokens
        origin: "2",
        token: paymentData.token,
        total: paymentData.amount.toString(),
        description: paymentData.description || "Token-based payment",
        ...(paymentData.customerInfo?.email && { email: paymentData.customerInfo.email }),
        ...(paymentData.customerInfo?.name && { name: paymentData.customerInfo.name }),
        ...(paymentData.customerInfo?.address && {
          address1: paymentData.customerInfo.address.line1,
          address2: paymentData.customerInfo.address.line2,
          city: paymentData.customerInfo.address.city,
          state: paymentData.customerInfo.address.state,
          zip: paymentData.customerInfo.address.zip,
          country: paymentData.customerInfo.address.country,
        }),
      };

      logger.info("Transaction data prepared", {
        merchantId: paymentData.merchantId,
        transactionType: "sale (token-based payment)",
        type: transactionData.type,
        total: transactionData.total,
        amount: paymentData.amount,
      });

      logger.info("Sending transaction request to Payrix", {
        url: `${PAYRIX_API_URL}/txns`,
        merchantId: paymentData.merchantId,
        amount: paymentData.amount,
        token: paymentData.token.substring(0, 8) + "...",
      });

      const response = await this.apiClient.post("/txns", transactionData);

      logger.info("Token payment response received", {
        status: response.status,
        responseStructure: {
          hasData: !!response.data,
          hasResponse: !!response.data?.response,
          hasResponseData: !!response.data?.response?.data,
          responseDataLength: Array.isArray(response.data?.response?.data) ? response.data.response.data.length : 0,
          hasErrors: !!response.data?.response?.errors,
          errorsLength: Array.isArray(response.data?.response?.errors) ? response.data.response.errors.length : 0,
        },
        fullResponse: response.data,
      });

      // Try multiple possible response structures
      let transactionData_response;

      // Standard structure: response.data.response.data[0]
      if (response.data?.response?.data?.[0]) {
        transactionData_response = response.data.response.data[0];
        logger.info("Found transaction data in standard structure", {
          transactionId: transactionData_response.id,
          status: transactionData_response.status,
        });
      }
      // Alternative structure: response.data.data[0]
      else if (response.data?.data?.[0]) {
        transactionData_response = response.data.data[0];
        logger.info("Found transaction data in alternative structure", {
          transactionId: transactionData_response.id,
          status: transactionData_response.status,
        });
      }
      // Direct structure: response.data (for single transaction responses)
      else if (response.data?.id) {
        transactionData_response = response.data;
        logger.info("Found transaction data in direct structure", {
          transactionId: transactionData_response.id,
          status: transactionData_response.status,
        });
      }

      // Check for Payrix API errors before looking for transaction data
      const errors = response.data?.response?.errors || response.data?.errors;
      if (errors && Array.isArray(errors) && errors.length > 0) {
        logger.error("Payrix API returned errors", {
          errors,
          amount: paymentData.amount,
          merchantId: paymentData.merchantId,
        });
        const errorMessages = errors.map((err: PayrixError) => `${err.field || "general"}: ${err.msg}`).join(", ");
        throw new Error(`Payrix API errors: ${errorMessages}`);
      }

      if (!transactionData_response) {
        logger.error("No transaction data found in any expected structure", {
          responseData: response.data,
          amount: paymentData.amount,
          hasErrors: !!errors,
          errorsCount: Array.isArray(errors) ? errors.length : 0,
          responseStatus: response.status,
        });
        throw new Error("Invalid Payrix response structure: no transaction data found");
      }

      // Cleanup token after successful transaction for PCI compliance
      await this.cleanupToken(paymentData.token);

      return {
        success: true,
        transaction: transactionData_response,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Token payment processing error", {
        merchantId: paymentData.merchantId,
        token: paymentData.token.substring(0, 8) + "...",
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      const errorData = axiosError.response?.data as { message?: string } | undefined;

      // Cleanup token after failed transaction for PCI compliance
      await this.cleanupToken(paymentData.token);

      return {
        success: false,
        error: `Payment processing failed: ${errorData?.message || axiosError.message}`,
      };
    }
  }

  async cleanupToken(token: string): Promise<TokenDeletionResult> {
    try {
      logger.info("Cleaning up token for PCI compliance", {
        token: token.substring(0, 8) + "...",
      });

      const response = await this.apiClient.delete(`/tokens/${token}`);

      logger.info("Token cleanup successful", {
        token: token.substring(0, 8) + "...",
        status: response.status,
      });

      return {
        success: true,
        message: "Token deleted successfully",
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.warn("Token cleanup failed (non-critical)", {
        token: token.substring(0, 8) + "...",
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        message: axiosError.message,
        note: "Token may have already expired or been deleted",
      });

      // Token cleanup failure is non-critical - tokens have TTL
      return {
        success: false,
        error: `Token cleanup failed: ${axiosError.message}`,
      };
    }
  }

  async deleteToken(token: string): Promise<TokenDeletionResult> {
    try {
      logger.info("Deleting token", {
        token: token.substring(0, 8) + "...",
      });

      const response = await this.apiClient.delete(`/tokens/${token}`);

      logger.info("Token deletion successful", {
        token: token.substring(0, 8) + "...",
        status: response.status,
      });

      return { success: true };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Token deletion error", {
        token: token.substring(0, 8) + "...",
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      const errorData = axiosError.response?.data as { message?: string } | undefined;
      return {
        success: false,
        error: `Token deletion failed: ${errorData?.message || axiosError.message}`,
      };
    }
  }
}
