import { PayrixMerchantValidationService } from "../payrix/merchant-validation.service.js";
import { PayrixMerchantCreationService } from "../payrix/merchant-creation.service.js";
import { PayrixNoteManagementService } from "../payrix/note-management.service.js";
import { PayrixUserService } from "../payrix/user.service.js";
import { PayrixPaymentService } from "../payrix/payment.service.js";
import { PayrixMerchantService } from "../payrix/merchant.service.js";
import type {
  IPayrixMerchantValidationService,
  IPayrixMerchantCreationService,
  IPayrixNoteManagementService,
  IPayrixUserService,
  IPayrixPaymentService,
  IPayrixMerchantService,
  IServiceContainer,
} from "../interfaces/payrix-service.interfaces.js";

export class ServiceContainer implements IServiceContainer {
  private static instance: ServiceContainer;
  private merchantValidationService?: IPayrixMerchantValidationService;
  private merchantCreationService?: IPayrixMerchantCreationService;
  private noteManagementService?: IPayrixNoteManagementService;
  private userService?: IPayrixUserService;
  private paymentService?: IPayrixPaymentService;
  private merchantService?: IPayrixMerchantService;

  private constructor() {}

  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  public getMerchantValidationService(): IPayrixMerchantValidationService {
    if (!this.merchantValidationService) {
      this.merchantValidationService = new PayrixMerchantValidationService();
    }
    return this.merchantValidationService;
  }

  public getMerchantCreationService(): IPayrixMerchantCreationService {
    if (!this.merchantCreationService) {
      this.merchantCreationService = new PayrixMerchantCreationService();
    }
    return this.merchantCreationService;
  }

  public getNoteManagementService(): IPayrixNoteManagementService {
    if (!this.noteManagementService) {
      this.noteManagementService = new PayrixNoteManagementService();
    }
    return this.noteManagementService;
  }

  public getUserService(): IPayrixUserService {
    if (!this.userService) {
      this.userService = new PayrixUserService();
    }
    return this.userService;
  }

  public getPaymentService(): IPayrixPaymentService {
    if (!this.paymentService) {
      this.paymentService = new PayrixPaymentService();
    }
    return this.paymentService;
  }

  public getMerchantService(): IPayrixMerchantService {
    if (!this.merchantService) {
      this.merchantService = new PayrixMerchantService();
    }
    return this.merchantService;
  }

  public setMerchantValidationService(service: IPayrixMerchantValidationService): void {
    this.merchantValidationService = service;
  }

  public setMerchantCreationService(service: IPayrixMerchantCreationService): void {
    this.merchantCreationService = service;
  }

  public setNoteManagementService(service: IPayrixNoteManagementService): void {
    this.noteManagementService = service;
  }

  public setUserService(service: IPayrixUserService): void {
    this.userService = service;
  }

  public setPaymentService(service: IPayrixPaymentService): void {
    this.paymentService = service;
  }

  public setMerchantService(service: IPayrixMerchantService): void {
    this.merchantService = service;
  }

  public reset(): void {
    this.merchantValidationService = undefined;
    this.merchantCreationService = undefined;
    this.noteManagementService = undefined;
    this.userService = undefined;
    this.paymentService = undefined;
    this.merchantService = undefined;
  }
}
